import { SiGithub } from "@icons-pack/react-simple-icons";
import { createFileRoute } from "@tanstack/react-router";
import { ExternalLink, Lock } from "lucide-react";
import Cursor from "@/components/cursor";
import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";

export const Route = createFileRoute("/projects")({
    component: Projects,
});

interface Project {
    title: string;
    description: string;
    status: "live" | "coming-soon" | "portfolio";
    isOpenSource: boolean;
    url?: string;
    githubUrl?: string;
}

const projects: Project[] = [
    {
        title: "catpics.lol / catpictur.es",
        description:
            "a little 2 person project with a friend where you are able to scroll through 100k+ cat pictures and you can keep your favourites.",
        status: "live",
        isOpenSource: false,
        url: "https://catpics.lol",
    },
    {
        title: "occo.rocks",
        description:
            "the very portfolio and website you're looking at right now? it's this one.",
        status: "portfolio",
        isOpenSource: true,
        url: "https://occo.rocks",
        githubUrl: "https://github.com/occorune/portfolio",
    },
    {
        title: "control your privacy and online presence",
        description:
            "WIP Project - a platform that puts your privacy first while helping you grow your online presence, made by two privacy-focused developers.",
        status: "coming-soon",
        isOpenSource: false,
    },
    {
        title: "we're back...",
        description:
            "keep an eye out.",
        status: "coming-soon",
        isOpenSource: true,
    },
];

function Projects() {
    return (
        <div className="min-h-screen bg-background text-white font-primary overflow-hidden relative w-full">
            <Cursor />
            <main className="container mx-auto px-4 pt-20 flex flex-col justify-center min-h-screen">
                <div className="max-w-6xl mx-auto w-full">
                    <div className="text-center mb-12">
                        <h1 className="text-4xl md:text-6xl font-bold mb-4">
                            my projects
                        </h1>
                        <p className="text-lg text-white/75 max-w-2xl mx-auto">
                            here's some stuff i've built or am working on. most of my repos
                            are private because i'm paranoid, but here's what i can show you.
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-6 mb-12">
                        {projects.map((project, index) => (
                            <Card
                                key={index}
                                className="bg-white/5 border-white/10 hover:bg-white/10 transition-colors cursor-target"
                            >
                                <CardHeader>
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <CardTitle className="text-xl font-semibold text-white">
                                                {project.title}
                                            </CardTitle>
                                            <div className="flex items-center gap-2 mt-2">
                                                <span
                                                    className={`px-2 py-1 text-xs rounded-full ${project.status === "live"
                                                        ? "bg-green-500/20 text-green-400 border border-green-500/30"
                                                        : project.status === "coming-soon"
                                                            ? "bg-yellow-500/20 text-yellow-400 border border-yellow-500/30"
                                                            : "bg-blue-500/20 text-blue-400 border border-blue-500/30"
                                                        }`}
                                                >
                                                    {project.status === "live"
                                                        ? "Live"
                                                        : project.status === "coming-soon"
                                                            ? "Coming Soon"
                                                            : "Portfolio"}
                                                </span>
                                                <span
                                                    className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 ${project.isOpenSource
                                                        ? "bg-green-500/20 text-green-400 border border-green-500/30"
                                                        : "bg-red-500/20 text-red-400 border border-red-500/30"
                                                        }`}
                                                >
                                                    {project.isOpenSource ? (
                                                        <>
                                                            <SiGithub className="w-3 h-3" />
                                                            Open Source
                                                        </>
                                                    ) : (
                                                        <>
                                                            <Lock className="w-3 h-3" />
                                                            Closed Source
                                                        </>
                                                    )}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <CardDescription className="text-white/75 mt-3">
                                        {project.description}
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex gap-2">
                                        {project.url && (
                                            <Button variant="outline" size="sm" asChild>
                                                <a
                                                    href={project.url}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="flex items-center gap-2"
                                                >
                                                    <ExternalLink className="w-4 h-4" />
                                                    Visit
                                                </a>
                                            </Button>
                                        )}
                                        {project.githubUrl && (
                                            <Button variant="outline" size="sm" asChild>
                                                <a
                                                    href={project.githubUrl}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="flex items-center gap-2"
                                                >
                                                    <SiGithub className="w-4 h-4" />
                                                    Source
                                                </a>
                                            </Button>
                                        )}
                                        {project.status === "coming-soon" && !project.url && (
                                            <Button variant="outline" size="sm" disabled>
                                                Coming Soon
                                            </Button>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </main>
        </div>
    );
}