/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as ProjectsRouteImport } from './routes/projects'
import { Route as AboutRouteImport } from './routes/about'
import { Route as IndexRouteImport } from './routes/index'
import { Route as BlogIndexRouteImport } from './routes/blog/index'
import { Route as BlogIdRouteImport } from './routes/blog/$id'
import { Route as AuthLoginRouteImport } from './routes/auth/login'

const ProjectsRoute = ProjectsRouteImport.update({
  id: '/projects',
  path: '/projects',
  getParentRoute: () => rootRouteImport,
} as any)
const AboutRoute = AboutRouteImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const BlogIndexRoute = BlogIndexRouteImport.update({
  id: '/blog/',
  path: '/blog/',
  getParentRoute: () => rootRouteImport,
} as any)
const BlogIdRoute = BlogIdRouteImport.update({
  id: '/blog/$id',
  path: '/blog/$id',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthLoginRoute = AuthLoginRouteImport.update({
  id: '/auth/login',
  path: '/auth/login',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/projects': typeof ProjectsRoute
  '/auth/login': typeof AuthLoginRoute
  '/blog/$id': typeof BlogIdRoute
  '/blog': typeof BlogIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/projects': typeof ProjectsRoute
  '/auth/login': typeof AuthLoginRoute
  '/blog/$id': typeof BlogIdRoute
  '/blog': typeof BlogIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/projects': typeof ProjectsRoute
  '/auth/login': typeof AuthLoginRoute
  '/blog/$id': typeof BlogIdRoute
  '/blog/': typeof BlogIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/about'
    | '/projects'
    | '/auth/login'
    | '/blog/$id'
    | '/blog'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/about' | '/projects' | '/auth/login' | '/blog/$id' | '/blog'
  id:
    | '__root__'
    | '/'
    | '/about'
    | '/projects'
    | '/auth/login'
    | '/blog/$id'
    | '/blog/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AboutRoute: typeof AboutRoute
  ProjectsRoute: typeof ProjectsRoute
  AuthLoginRoute: typeof AuthLoginRoute
  BlogIdRoute: typeof BlogIdRoute
  BlogIndexRoute: typeof BlogIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/projects': {
      id: '/projects'
      path: '/projects'
      fullPath: '/projects'
      preLoaderRoute: typeof ProjectsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/blog/': {
      id: '/blog/'
      path: '/blog'
      fullPath: '/blog'
      preLoaderRoute: typeof BlogIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/blog/$id': {
      id: '/blog/$id'
      path: '/blog/$id'
      fullPath: '/blog/$id'
      preLoaderRoute: typeof BlogIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth/login': {
      id: '/auth/login'
      path: '/auth/login'
      fullPath: '/auth/login'
      preLoaderRoute: typeof AuthLoginRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AboutRoute: AboutRoute,
  ProjectsRoute: ProjectsRoute,
  AuthLoginRoute: AuthLoginRoute,
  BlogIdRoute: BlogIdRoute,
  BlogIndexRoute: BlogIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
