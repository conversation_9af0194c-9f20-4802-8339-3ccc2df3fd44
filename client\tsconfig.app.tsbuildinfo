{"root": ["./src/env.ts", "./src/main.tsx", "./src/routetree.gen.ts", "./src/vite-env.d.ts", "./src/components/404.tsx", "./src/components/background.tsx", "./src/components/cursor.tsx", "./src/components/discord-status.tsx", "./src/components/navbar.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/input.tsx", "./src/lib/auth.ts", "./src/lib/utils.ts", "./src/routes/__root.tsx", "./src/routes/about.tsx", "./src/routes/index.tsx", "./src/routes/projects.tsx", "./src/routes/auth/login.tsx", "./src/routes/blog/$id.tsx", "./src/routes/blog/index.tsx", "./src/utils/fetch.ts"], "errors": true, "version": "5.7.3"}